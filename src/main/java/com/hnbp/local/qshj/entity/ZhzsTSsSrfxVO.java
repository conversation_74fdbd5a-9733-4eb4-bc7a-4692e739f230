package com.hnbp.local.qshj.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class ZhzsTSsSrfxVO extends ZhzsTSsSrfxDO {
    /** 税款所属期起 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime fskssqq;

    /** 税款所属期止 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime fskssqz ;

    /** 入库日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private LocalDateTime frkrq ;

    /** 合计 */
    // @JsonSerialize(using = BigDecimalSerializerYuanToWanYuan.class)
    private BigDecimal fhj;

}

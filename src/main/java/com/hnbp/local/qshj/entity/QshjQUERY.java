package com.hnbp.local.qshj.entity;


import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.Optional;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class QshjQUERY extends BaseQUERY {
    private String fkfgsmc;

    private String fxmmc;

    private String fdh;

    private String fyssj;
    private LocalDateTime fyssjStart;
    private LocalDateTime fyssjEnd;

    private BigDecimal fxsjdMin;
    private BigDecimal fxsjdMax;

    private String fqszt;
    private List<String> fqsztList;

    private String fzspm;
    private List<String> fzspmList;

    public QshjQUERY handleConditionYssj() {
        Optional.ofNullable(this.fyssj)
                .filter(StringUtils::isNotBlank)
                .map(str -> str.split(defaultRangeJoiner))
                .ifPresent(arr -> {
                    this.setFyssjStart(super.convertToStartDate(arr[0]));
                    this.setFyssjEnd(super.convertToEndDate(arr[1]));
                });
        return this;
    }

    public QshjQUERY handleConditionQszt() {
        Optional.ofNullable(this.fqszt)
                .filter(StringUtils::isNotBlank)
                .map(str -> str.split(defaultItemSeparator))
                .map(Arrays::asList)
                .ifPresent(this::setFqsztList);
        return this;
    }

    public QshjQUERY handleConditionFzspm() {
        Optional.ofNullable(this.fzspm)
                .filter(StringUtils::isNotBlank)
                .map(str -> str.split(defaultItemSeparator))
                .map(Arrays::asList)
                .ifPresent(this::setFzspmList);
        return this;
    }
}

package com.hnbp.local.qshj.entity;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.beans.BeanUtils;

import javax.persistence.Column;
import javax.persistence.OrderBy;
import javax.persistence.Table;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
* 收入分析;
* @LocalDateTime : 2023-11-11 09:56:33
*/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Table(name="tb_dw_srfx_srfx_main")
public class ZhzsTSsSrfxDO implements Serializable {
   /** 主键 */
   @Column(name="FID")
   private String fid ;

   /** 税款所属期起 */
   @Column(name="fskssqq")
   private LocalDateTime fskssqq ;

   /** 税款所属期止 */
   @Column(name="fskssqz")
   private LocalDateTime fskssqz ;

   /** 入库日期 */
   @Column(name="frkrq")
   @OrderBy("DESC")
   private LocalDateTime frkrq ;

   /** 纳税人名称 */
   @Column(name="fnsrmc")
   private String fnsrmc ;

   /** 纳税人识别号 */
   @Column(name="fnsrsbh")
   private String fnsrsbh ;

   /** 行业门类 */
   @Column(name="fhyml")
   private String fhyml ;

   /** 行业大类 */
   @Column(name="fhydl")
   private String fhydl ;

   /** 行业中类 */
   @Column(name="fhyzl")
   private String fhyzl ;

   /** 行业 */
   @Column(name="fhy")
   private String fhy ;

   /** 收款国库 */
   @Column(name="fskgk")
   private String fskgk ;

   /** 征收项目 */
   @Column(name="fzsxm")
   private String fzsxm ;

   /** 征收品目 */
   @Column(name="fzspm")
   private String fzspm ;

   /** 街道乡镇 */
   @Column(name="fjdxz")
   private String fjdxz ;

   /** 登记注册类型 */
   @Column(name="fdjzclx")
   private String fdjzclx ;

   /** 预算科目 */
   @Column(name="fyskm")
   private String fyskm ;

   /** 税率 */
   @Column(name="fsl")
   private String fsl ;

   /** 主管税务所 */
   @Column(name="fzgsws")
   private String fzgsws;

   /** 所属税务机关 */
   @Column(name="fssswjg")
   private String fssswjg;

   /** 征收税务机关 */
   @Column(name="fzsswjg")
   private String fzsswjg;

   /** 税款属性 */
   @Column(name="fsksx")
   private String fsksx ;

   /** 原收款国库 */
   @Column(name="fyskgk")
   private String fyskgk ;

   /** 耕地占用税 */
   @Column(name="ftdzzs")
   private BigDecimal fgdzys ;

   /** 车船税 */
   @Column(name="fccs")
   private BigDecimal fccs ;

   /** 契税 */
   @Column(name="fqs")
   private BigDecimal fqs ;

   /** 城市维护建设税 */
   @Column(name="fcswhjss")
   private BigDecimal fcswhjsh ;

   /** 烟叶税 */
   @Column(name="fyys")
   private BigDecimal fyys ;

   /** 个人所得税 */
   @Column(name="fgrsds")
   private BigDecimal fgrsds ;

   /** 地方教育附加 */
   @Column(name="fdfjyfj")
   private BigDecimal fdfjyfj ;

   /** 房产税 */
   @Column(name="ffcs")
   private BigDecimal ffcs ;

   /** 印花税 */
   @Column(name="fyhs")
   private BigDecimal fyhs ;

   /** 资源税 */
   @Column(name="fzys")
   private BigDecimal fzys ;

   /** 土地增值税 */
   @Column(name="ftdzzs")
   private BigDecimal ftdzzs ;

   /** 企业所得税 */
   @Column(name="fqysds")
   private BigDecimal fqysds ;

   /** 城镇土地使用税 */
   @Column(name="fcztdsys")
   private BigDecimal fcztdsys ;

   /** 城市维护建设税 */
   @Column(name="fcswhjss")
   private BigDecimal fcswhjss ;

   /** 教育费附加 */
   @Column(name="fjyffj")
   private BigDecimal fjyffj ;

   /** 增值税 */
   @Column(name="fzzs")
   private BigDecimal fzzs ;

   /** 环境保护税 */
   @Column(name="fhjbhs")
   private BigDecimal fhjbhs ;

   /** 车辆购置税 */
   @Column(name="fclgzs")
   private BigDecimal fclgzs ;

   /** 消费税 */
   @Column(name="fxfs")
   private BigDecimal fxfs ;

   /** 中央级 */
   @Column(name="fzyj")
   private BigDecimal fzyj ;

   /** 省市级 */
   @Column(name="fssj")
   private BigDecimal fssj ;

   /** 地市级 */
   @Column(name="fdsj")
   private BigDecimal fdsj ;

   /** 区县级 */
   @Column(name="fqxj")
   private BigDecimal fqxj ;

   /** 乡镇级 */
   @Column(name="fxzj")
   private BigDecimal fxzj ;

   /** 计税依据 */
   @Column(name="fjsyj")
   private BigDecimal fjsyj ;

   /** 费合计 */
   @Column(name="ffhj")
   private BigDecimal ffhj ;

   /** 税合计 */
   @Column(name="fshj")
   private BigDecimal fshj ;

   /** 合计 */
   @Column(name="fhj")
   private BigDecimal fhj ;

   /** 地方收入 */
   @Column(name="fdfsr")
   private BigDecimal fdfsr ;

   /** 可用财力 */
   @Column(name="fkycl")
   private BigDecimal fkycl ;

   /** 数据来源 */
   @Column(name="fsjly")
   private String fsjly ;

   /** 经济成分 */
   @Column(name="fjjcf")
   private String fjjcf ;

   public ZhzsTSsSrfxVO buildVO() {
       ZhzsTSsSrfxVO vo = new ZhzsTSsSrfxVO();
       BeanUtils.copyProperties(this, vo);

       return vo;
   }

}
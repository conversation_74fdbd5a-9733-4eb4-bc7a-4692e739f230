package com.hnbp.local.qshj.mapper;

import com.github.pagehelper.Page;
import com.hnbp.local.qshj.entity.QshjQUERY;
import com.hnbp.local.qshj.entity.ZhzsTSsSrfxDO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

@Mapper
public interface LiquidationPhaseMapper {
    Page<Map<String, Object>> overview(QshjQUERY query);

    Page<Map<String, Object>> analyzeByXm(QshjQUERY query);

    Page<Map<String, Object>> analyzeByLd(QshjQUERY query);

    Page<ZhzsTSsSrfxDO> ssDetail(QshjQUERY query);

    Integer confirmLiquidation(Map<String, String> temp);
}

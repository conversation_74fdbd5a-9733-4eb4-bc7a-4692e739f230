package com.hnbp.local.qshj.controller;

import com.github.pagehelper.Page;
import com.hnbp.common.core.domain.BpResponse;
import com.hnbp.common.core.domain.ResultMsg;
import com.hnbp.local.qshj.entity.QshjQUERY;
import com.hnbp.local.qshj.service.LiquidationPhaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;

/**
 * 清算环节
 * 从浏阳市迁移
 */
@RestController
@RequestMapping("/liquidationPhase")
@Api(tags = "清算环节")
public class LiquidationPhaseController {
    @Autowired
    private LiquidationPhaseService liquidationPhaseService;

    @PostMapping("/overview")
    public BpResponse overview(@RequestBody QshjQUERY query) {
        Page<Map<String, Object>> result = liquidationPhaseService.overview(query);
        return BpResponse.ok(result);
    }

    @PostMapping("/analyzeByXm")
    public BpResponse analyzeByXm(@RequestBody QshjQUERY query) {
        Page<Map<String, Object>> result = liquidationPhaseService.analyzeByXm(query);
        return BpResponse.ok(result);
    }

    @PostMapping("/analyzeByLd")
    public BpResponse analyzeByLd(@RequestBody QshjQUERY query) {
        Page<Map<String, Object>> result = liquidationPhaseService.analyzeByLd(query);
        return BpResponse.ok(result);
    }

    @PostMapping("/ssDetail")
    public BpResponse ssDetail(@RequestBody QshjQUERY query) {
        String fkfgsmc = query.getFkfgsmc();
        if (StringUtils.isEmpty(fkfgsmc)) {
            throw new RuntimeException("开发商名称不能为空！");
        }
        return BpResponse.ok(liquidationPhaseService.ssDetail(query));
    }

    @ApiOperation("确认清算")
    @RequestMapping(value = "/confirmLiquidation", method = RequestMethod.POST)
    public ResultMsg insertYqsxm(
            @ApiParam(value = "确认清算数据")
            @RequestBody List<Map> data)throws Exception {
        try {
            liquidationPhaseService.confirmLiquidation(data);
        }catch (Exception e) {
            throw new Exception("确认清算失败!", e);
        }
        return ResultMsg.success("确认清算成功!");
    }

}

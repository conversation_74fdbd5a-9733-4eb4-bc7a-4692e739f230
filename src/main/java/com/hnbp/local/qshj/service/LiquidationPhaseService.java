package com.hnbp.local.qshj.service;

import com.github.pagehelper.Page;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import com.hnbp.local.qshj.entity.QshjQUERY;
import com.hnbp.local.qshj.entity.ZhzsTSsSrfxDO;
import com.hnbp.local.qshj.entity.ZhzsTSsSrfxVO;
import com.hnbp.local.qshj.mapper.LiquidationPhaseMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.*;

@Service
public class LiquidationPhaseService {
    @Autowired
    private LiquidationPhaseMapper liquidationPhaseMapper;

    public Page<Map<String, Object>> overview(QshjQUERY query) {
        query.handleConditionYssj().handleConditionQszt();
        PageHelper.startPage(query.getPage(), query.getLimit());
        Page<Map<String, Object>> result = liquidationPhaseMapper.overview(query);
        return result;
    }

    public Page<Map<String, Object>> analyzeByXm(QshjQUERY query) {
        query.handleConditionYssj().handleConditionQszt();
        PageHelper.startPage(query.getPage(), query.getLimit());
        Page<Map<String, Object>> result = liquidationPhaseMapper.analyzeByXm(query);
        return result;
    }

    public Page<Map<String, Object>> analyzeByLd(QshjQUERY query) {
        query.handleConditionYssj().handleConditionQszt();
        PageHelper.startPage(query.getPage(), query.getLimit());
        Page<Map<String, Object>> result = liquidationPhaseMapper.analyzeByLd(query);
        return result;
    }

    public PageInfo<ZhzsTSsSrfxVO> ssDetail(QshjQUERY query) {
        query.handleConditionFzspm().handleDateCondition();

        PageHelper.startPage(query.getPage(), query.getLimit());
        Page<ZhzsTSsSrfxDO> srfxPage = liquidationPhaseMapper.ssDetail(query);

        return srfxPage.toPageInfo(ZhzsTSsSrfxDO::buildVO);
    }

    public void confirmLiquidation(List<Map> data) {
        for (Map map : data) {
            Map<String, String> temp = new HashMap();
            temp.put("qszt", "已清算");
            temp.put("fkfgs", map.get("fkfgsmc").toString());
            temp.put("fxmmc", map.get("fxmmc").toString());
            liquidationPhaseMapper.confirmLiquidation(temp);
        }
    }
}

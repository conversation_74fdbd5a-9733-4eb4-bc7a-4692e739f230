# Spring
spring:
  cloud:
    nacos:
      discovery:
        # 服务注册地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: 562bf7b3-99a5-409a-9b96-07f9458890ff
        group: ZHOU_YONGSHNEG
      config:
        # 配置中心地址
        server-addr: 127.0.0.1:8848
        # 命名空间
        namespace: 562bf7b3-99a5-409a-9b96-07f9458890ff
        # 配置文件格式
        file-extension: yml
        # 共享配置
        shared-configs:
          - application-biaopu-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
          - application-${spring.profiles.active}.${spring.cloud.nacos.config.file-extension}
      username: nacos
      password: <PERSON><PERSON><PERSON><PERSON>@8888

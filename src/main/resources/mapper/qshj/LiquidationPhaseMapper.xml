<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.hnbp.local.qshj.mapper.LiquidationPhaseMapper">
    <select id="overview" resultType="java.util.Map">
        WITH
        ysxkz AS (
            SELECT * FROM (
                SELECT FXMMC, MAX(FKFGSMC) fkfgsmc, MIN(ffzrq) fyssjq, MAX(ffzrq) fyssjz, SUM(fysmj) fzysmj
                FROM B_T_YXYSXKZFFXX
                <where>
                    <if test="fkfgsmc != null and fkfgsmc != ''">
                        FKFGSMC LIKE CONCAT('%', #{fkfgsmc}, '%')
                    </if>
                </where>
                GROUP BY FXMMC
            ) tmp1
            <where>
                <if test="fyssjStart != null">
                    AND fyssjq >= #{fyssjStart}
                </if>
                <if test="fyssjEnd != null">
                    AND fyssjq &lt;= #{fyssjEnd}
                </if>
            </where>
        ),
        wqxx AS (
            SELECT ysxkz.FXMMC AS FXMMC_wqxx, SUM(wq.FHSMJ) AS fzhsmj, ROUND(SUM(wq.fcjje)/10000, 2) AS fzcjje FROM ysxkz LEFT JOIN B_T_YXFCWQXSXX wq ON ysxkz.FXMMC = wq.FXMMC
            GROUP BY ysxkz.FXMMC
        ),
        qszt AS (
            SELECT ysxkz.FXMMC AS FXMMC_qszt, ysxkz.fkfgsmc AS fkfgsmc_qszt, MAX(t2.QSZT) QSZT
            FROM ysxkz
            LEFT JOIN ZHZS_BILL_LPWHXX t2 ON ysxkz.FXMMC = t2.FXMMC AND ysxkz.fkfgsmc = t2.FKFGS
            GROUP BY ysxkz.FXMMC, ysxkz.fkfgsmc <!-- 就这么写算了_(¦3」∠)_ -->
        ),
        ss AS (
            SELECT t1.fkfgsmc AS fkfgsmc_ss, ROUND(SUM(合计)/10000, 2) AS fqsje
            FROM (SELECT DISTINCT fkfgsmc FROM ysxkz) t1
            LEFT JOIN ZHZS_T_SS_SRFX srfx ON t1.fkfgsmc = srfx.纳税人名称 and 征收项目 = '土地增值税' and instr(征收品目,'清算')> 0
            GROUP BY t1.fkfgsmc
        )
        SELECT *,
            COALESCE(
                QSZT,
                CASE
                    WHEN fxsjd IS NULL THEN '/'
                    WHEN fxsjd >=100 THEN '必须清算'
                    WHEN fxsjd >= 85 OR DATE_ADD(fyssjz, INTERVAL 3 YEAR) &lt;= NOW() THEN '可清算'
                    ELSE '/'
                END
           ) AS fqszt
        FROM (
            SELECT
                *
                , CONCAT(fyssjq, '至', fyssjz) AS fyssj
                , CASE
                    WHEN fzysmj = 0 THEN NULL
                    ELSE (fzhsmj/fzysmj)*100
                END AS fxsjd
            FROM ysxkz
            LEFT JOIN wqxx ON ysxkz.FXMMC = wqxx.FXMMC_wqxx
            LEFT JOIN qszt ON ysxkz.FXMMC = qszt.FXMMC_qszt AND ysxkz.fkfgsmc = qszt.fkfgsmc_qszt
            LEFT JOIN ss ON ysxkz.fkfgsmc = ss.fkfgsmc_ss
        ) tmp1
        <where>
            <if test="fxsjdMin != null">
                fxsjd >= #{fxsjdMin}
            </if>
            <if test="fxsjdMax != null">
                fxsjd &lt;= #{fxsjdMax}
            </if>
        </where>
        ORDER BY fkfgsmc
    </select>

    <select id="analyzeByXm" resultType="java.util.Map">
        WITH
        ysxkz AS (
            SELECT * FROM (
                SELECT FXMMC, MAX(FKFGS) AS fkfgsmc, MIN(FFZRQ) fyssjq, MAX(FFZRQ) fyssjz, SUM(FYSZMJ) fzysmj
                FROM TB_DW_JAFDC_YSXKZ_GEN
                <where>
                    <if test="fkfgsmc != null and fkfgsmc != ''">
                        AND FKFGS LIKE CONCAT('%', #{fkfgsmc}, '%')
                    </if>
                    <if test="fxmmc != null and fxmmc != ''">
                        AND FXMMC LIKE CONCAT('%', #{fxmmc}, '%')
                    </if>
                </where>
                GROUP BY FXMMC
            ) tmp1
            <where>
                <if test="fyssjStart != null">
                    AND fyssjq >= #{fyssjStart}
                </if>
                <if test="fyssjEnd != null">
                    AND fyssjq &lt;= #{fyssjEnd}
                </if>
            </where>
        ),
        wqxx AS (
            SELECT t1.FXMMC AS FXMMC_wqxx, SUM(wq.FHSMJ) AS fzhsmj, ROUND(SUM(wq.FHTJE)/10000, 2) AS fzcjje FROM (
                SELECT DISTINCT FXMMC, FXKZBH FROM TB_DW_JAFDC_YSXKZ_GEN WHERE FXMMC IN (SELECT DISTINCT FXMMC FROM ysxkz)
            ) t1 LEFT JOIN TB_DW_JAFDC_WQHTXX_GEN wq ON t1.FXKZBH = wq.FYSZH
            GROUP BY t1.FXMMC
        ),
        qszt AS (
            SELECT ysxkz.FXMMC AS FXMMC_qszt, ysxkz.fkfgsmc AS fkfgsmc_qszt, MAX(t2.QSZT) QSZT
            FROM ysxkz
            LEFT JOIN ZHZS_BILL_LPWHXX t2 ON ysxkz.FXMMC = t2.FXMMC AND ysxkz.fkfgsmc = t2.FKFGS
            GROUP BY ysxkz.FXMMC, ysxkz.fkfgsmc
        ),
        ss AS (
            SELECT fkfgsmc AS fkfgsmc_ss, ROUND(SUM(CASE WHEN ftax_type = 'tdzzs' THEN frkhj ELSE 0 END) / 10000, 2)  AS frktdzzs
            FROM (
                SELECT fkfgsmc,
                    CASE
                        WHEN fzsxm = '土地增值税' AND (fzspm LIKE '%核定%' OR fzspm LIKE '%清算%' OR fzspm LIKE '%尾盘%' ) THEN 'tdzzs'
                        ELSE 'others'
                    END AS ftax_type,
                    SUM(fhj) AS frkhj
                FROM (
                    SELECT nsrmd.fkfgsmc, srfx.fhj, srfx.fzsxm, srfx.fzspm
                    FROM (SELECT DISTINCT fkfgsmc FROM ysxkz) nsrmd
                    LEFT JOIN TB_DW_SRFX_SRFX_MAIN srfx ON nsrmd.fkfgsmc = srfx.FNSRMC
                ) tmp1
                GROUP BY fkfgsmc,
                    CASE
                        WHEN fzsxm = '土地增值税' AND (fzspm LIKE '%核定%' OR fzspm LIKE '%清算%' OR fzspm LIKE '%尾盘%' ) THEN 'tdzzs'
                        ELSE 'others'
                    END
            ) tmp2
            GROUP BY fkfgsmc
        )
        SELECT */*fxmmc, fkfgsmc, fzysmj, fzhsmj, fzcjje, frktdzzs, fqszt*/
        FROM (
        SELECT *,
            COALESCE(
                QSZT,
                CASE
                    WHEN fxsjd IS NULL THEN '未达到清算标准'
                    WHEN fxsjd >=100 THEN '可清算'
                    WHEN fxsjd >= 85 OR DATE_ADD(fyssjz, INTERVAL 3 YEAR) &lt;= NOW() THEN '可清算'
                    ELSE '未达到清算标准'
                END
           ) AS fqszt
        FROM (
            SELECT
                *
                , DATE_FORMAT(fyssjq,'YYYY-MM-DD') || '至' || DATE_FORMAT(fyssjz,'YYYY-MM-DD') AS fyssj
                , CASE
                    WHEN fzysmj = 0 THEN NULL
                    ELSE (fzhsmj/fzysmj)*100
                END AS fxsjd
            FROM ysxkz
            LEFT JOIN wqxx ON ysxkz.FXMMC = wqxx.FXMMC_wqxx
            LEFT JOIN qszt ON ysxkz.FXMMC = qszt.FXMMC_qszt AND ysxkz.fkfgsmc = qszt.fkfgsmc_qszt
            LEFT JOIN ss ON ysxkz.fkfgsmc = ss.fkfgsmc_ss
        )tmp1 ) tmp2
        <where>
            <if test="fxsjdMin != null">
                AND fxsjd >= #{fxsjdMin}
            </if>
            <if test="fxsjdMax != null">
                AND fxsjd &lt;= #{fxsjdMax}
            </if>
            <if test="fqsztList != null">
                AND fqszt IN
                <foreach collection="fqsztList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        ORDER BY fkfgsmc
    </select>

    <select id="analyzeByLd" resultType="java.util.Map">
        WITH
        ysxkz AS (
            SELECT * FROM (
            SELECT FXMMC, COALESCE(FDH, '未匹配楼栋') AS FDH, MAX(FKFGS) AS fkfgsmc, MIN(FFZRQ) fyssjq, MAX(FFZRQ) fyssjz, SUM(FYSZMJ) fzysmj, MAX(FXKZBH) fxkzbh
            FROM TB_DW_JAFDC_YSXKZ_GEN
            <where>
                <if test="fkfgsmc != null and fkfgsmc != ''">
                    AND FKFGS LIKE CONCAT('%', #{fkfgsmc}, '%')
                </if>
                <if test="fxmmc != null and fxmmc != ''">
                    AND FXMMC LIKE CONCAT('%', #{fxmmc}, '%')
                </if>
                <if test="fdh != null and fdh != ''">
                    AND FDH LIKE CONCAT('%', #{fdh}, '%')
                </if>
            </where>
            GROUP BY FXMMC, COALESCE(FDH, '未匹配楼栋')) tmp1
            <where>
                <if test="fyssjStart != null">
                    AND fyssjq >= #{fyssjStart}
                </if>
                <if test="fyssjEnd != null">
                    AND fyssjq &lt;= #{fyssjEnd}
                </if>
            </where>
        ),
        wqxx AS (
            SELECT t1.FXMMC AS FXMMC_wqxx, t1.FDH AS FDH_wqxx, MAX(FKFSMC) AS FKFSMC_wqxx, SUM(wq.FHSMJ) AS fzhsmj, ROUND(SUM(wq.FHTJE)/10000, 2) AS fzcjje FROM (
                SELECT DISTINCT FXMMC, COALESCE(FDH, '未匹配楼栋') AS FDH, FXKZBH FROM TB_DW_JAFDC_YSXKZ_GEN WHERE (FXMMC, COALESCE(FDH, '未匹配楼栋')) IN (SELECT DISTINCT FXMMC, FDH FROM ysxkz)
            ) t1 LEFT JOIN TB_DW_JAFDC_WQHTXX_GEN wq ON t1.FXKZBH = wq.FYSZH
            GROUP BY t1.FXMMC, t1.FDH
        ),
        qszt AS (
            SELECT ysxkz.FXMMC AS FXMMC_qszt, ysxkz.fkfgsmc AS fkfgsmc_qszt, MAX(t2.QSZT) QSZT
            FROM ysxkz
            LEFT JOIN ZHZS_BILL_LPWHXX t2 ON ysxkz.FXMMC = t2.FXMMC AND ysxkz.fkfgsmc = t2.FKFGS
            GROUP BY ysxkz.FXMMC, ysxkz.fkfgsmc
        )
        SELECT
        	CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN FKFSMC_wqxx
        		WHEN 1 THEN FKFSMC_wqxx
        		WHEN 2 THEN FKFSMC_wqxx
        		WHEN 3 THEN '合计'
        	END AS FKFSMC_wqxx,
        	CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
	        	WHEN 0 THEN FXMMC_wqxx
        		WHEN 1 THEN FXMMC_wqxx
        		WHEN 2 THEN '企业小计'
        		WHEN 3 THEN '/'
        	END AS FXMMC_wqxx,
        	CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN FDH_wqxx
        		WHEN 1 THEN '项目小计'
        		WHEN 2 THEN '/'
        		WHEN 3 THEN '/'
        	END AS FDH_wqxx,
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fxkzbh
        		WHEN 1 THEN '/'
        		WHEN 2 THEN '/'
        		WHEN 3 THEN '/'
        	END AS "fxkzbh",
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fyssjq
        		WHEN 1 THEN NULL
        		WHEN 2 THEN NULL
        		WHEN 3 THEN NULL
        	END AS "fyssjq",
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fyssjz
        		WHEN 1 THEN NULL
        		WHEN 2 THEN NULL
        		WHEN 3 THEN NULL
        	END AS "fyssjz",
        	SUM("fzysmj") AS fzysmj, SUM("fzhsmj") AS fzhsmj, SUM("fzcjje") AS fzcjje,
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fyssj
        		WHEN 1 THEN '/'
        		WHEN 2 THEN '/'
        		WHEN 3 THEN '/'
        	END AS "fyssj",
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fxsjd
        		WHEN 1 THEN NULL
        		WHEN 2 THEN NULL
        		WHEN 3 THEN NULL
        	END AS"fxsjd",
			CASE GROUPING(FKFSMC_wqxx) + GROUPING(FXMMC_wqxx) + GROUPING(FDH_wqxx)
        		WHEN 0 THEN fqszt
        		WHEN 1 THEN '/'
        		WHEN 2 THEN '/'
        		WHEN 3 THEN '/'
        	END AS "fqszt"
        FROM (
        SELECT *,
            COALESCE(
                QSZT,
                CASE
                    WHEN fxsjd IS NULL THEN '未达到清算标准'
                    WHEN fxsjd >=100 THEN '可清算'
                    WHEN fxsjd >= 85 OR DATE_ADD(fyssjz, INTERVAL 3 YEAR) &lt;= NOW() THEN '可清算'
                    ELSE '未达到清算标准'
                END
           ) AS fqszt
        FROM (
            SELECT
                COALESCE(FKFSMC_wqxx, fkfgsmc) FKFSMC_wqxx, COALESCE(FXMMC_wqxx, FXMMC) FXMMC_wqxx, COALESCE(FDH_wqxx, FDH) FDH_wqxx
                , fzysmj, fzhsmj, fzcjje, fyssjq, fyssjz, fxkzbh, qszt.qszt
                , CASE
                    WHEN ABS(DATEDIFF(fyssjz, fyssjq)) &lt;= 1 THEN DATE_FORMAT(fyssjq, '%Y-%m-%d')
                    WHEN fyssjq IS NOT NULL AND fyssjz IS NOT NULL THEN CONCAT(DATE_FORMAT(fyssjq, '%Y-%m-%d'), '至', DATE_FORMAT(fyssjz, '%Y-%m-%d'))
                    ELSE NULL
                END AS fyssj
                , CASE
                    WHEN fzysmj = 0 THEN NULL
                    ELSE (fzhsmj/fzysmj)*100
                END AS fxsjd
            FROM ysxkz
            FULL JOIN wqxx ON ysxkz.FXMMC = wqxx.FXMMC_wqxx AND ysxkz.FDH = wqxx.FDH_wqxx
            LEFT JOIN qszt ON ysxkz.FXMMC = qszt.FXMMC_qszt AND ysxkz.fkfgsmc = qszt.fkfgsmc_qszt
        )tmp1)tmp2
        <where>
            <if test="fxsjdMin != null">
                AND fxsjd >= #{fxsjdMin}
            </if>
            <if test="fxsjdMax != null">
                AND fxsjd &lt;= #{fxsjdMax}
            </if>
            <if test="fqsztList != null">
                AND fqszt IN
                <foreach collection="fqsztList" item="item" open="(" separator="," close=")">
                    #{item}
                </foreach>
            </if>
        </where>
        GROUP BY FKFSMC_wqxx, FXMMC_wqxx, FDH_wqxx WITH ROLLUP
        ORDER BY CASE WHEN FKFSMC_wqxx = '合计' THEN -9999 ELSE FKFSMC_wqxx END DESC,
        		CASE WHEN FXMMC_wqxx = '企业小计' THEN -9999 ELSE FXMMC_wqxx END DESC,
        		CASE WHEN FDH_wqxx='项目小计' THEN -9999 ELSE FDH_wqxx END DESC
    </select>

    <select id="ssDetail" resultType="com.hnbp.local.qshj.entity.ZhzsTSsSrfxDO">
        SELECT
            CASE GROUPING(FID) WHEN 0 THEN FID WHEN 1 THEN NULL END AS FID,
            CASE GROUPING(FID) WHEN 0 THEN FNSRMC WHEN 1 THEN '合计' END AS FNSRMC,
            CASE GROUPING(FID) WHEN 0 THEN FSKSSQQ WHEN 1 THEN NULL END AS FSKSSQQ,
            CASE GROUPING(FID) WHEN 0 THEN FSKSSQZ WHEN 1 THEN NULL END AS FSKSSQZ,
            CASE GROUPING(FID) WHEN 0 THEN FRKRQ WHEN 1 THEN NULL END AS FRKRQ,
            CASE GROUPING(FID) WHEN 0 THEN FZSXM WHEN 1 THEN NULL END AS FZSXM,
            CASE GROUPING(FID) WHEN 0 THEN FZSPM WHEN 1 THEN NULL END AS FZSPM,
            CASE GROUPING(FID) WHEN 0 THEN FHYDL WHEN 1 THEN NULL END AS FHYDL,
            CASE GROUPING(FID) WHEN 0 THEN FSKSX WHEN 1 THEN NULL END AS FSKSX,
            CASE GROUPING(FID) WHEN 0 THEN FSKGK WHEN 1 THEN NULL END AS FSKGK,
            SUM(FHJ) AS FHJ
        FROM TB_DW_SRFX_SRFX_MAIN
        WHERE FNSRMC = #{fkfgsmc}
        AND fzsxm = '土地增值税'
        <if test="fskssrqStart != null">
            AND fskssqq >= #{fskssrqStart}
        </if>
        <if test="fskssrqEnd != null">
            AND fskssqz &lt;= #{fskssrqEnd}
        </if>
        <if test="fzspmList != null">
            AND fzspm IN
            <foreach collection="fzspmList" item="item" open="(" separator="," close=")">
                #{item}
            </foreach>
        </if>
        GROUP BY FID WITH ROLLUP
        ORDER BY CASE FNSRMC WHEN '合计' THEN -9999 ELSE FNSRMC END ASC
    </select>

    <insert id="confirmLiquidation">
        INSERT INTO zhzs_bill_lpwhxx
        VALUES(#{fxmmc},#{fkfgs},#{qszt})
    </insert>

</mapper>